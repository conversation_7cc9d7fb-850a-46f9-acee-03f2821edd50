import React, { useState, useEffect } from 'react';
import { FinancialEvolutionData, FinancialSituationMonthData, FinancialSituationDataPoint } from '../../types/dashboard';
import { fetchFinancialEvolutionData } from '../../services/api';
import './FinancialEvolution.css';

interface TooltipData {
  x: number;
  y: number;
  month: string;
  profile: string;
  count: number;
  percentage: number;
  visible: boolean;
}

const FinancialEvolution: React.FC = () => {
  const [data, setData] = useState<FinancialEvolutionData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tooltip, setTooltip] = useState<TooltipData>({
    x: 0,
    y: 0,
    month: '',
    profile: '',
    count: 0,
    percentage: 0,
    visible: false
  });

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);
        const evolutionData = await fetchFinancialEvolutionData();
        setData(evolutionData);
      } catch (err) {
        setError('Erro ao carregar dados de evolução financeira');
        console.error('Error loading financial evolution data:', err);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Color mapping for financial profiles
  const profileColors: Record<string, string> = {
    'Undefined': '#8e8e93',
    'Overindebted': '#ff3b30',
    'Indebted': '#af52de',
    'Balanced': '#007aff',
    'Investor': '#34c759'
  };

  // Portuguese labels for financial profiles
  const profileLabels: Record<string, string> = {
    'Undefined': 'Não definido',
    'Overindebted': 'Superendividados',
    'Indebted': 'Endividados',
    'Balanced': 'Equilibrados',
    'Investor': 'Investidores'
  };

  const formatMonth = (monthStr: string): string => {
    const [year, month] = monthStr.split('-');
    const monthNames = [
      'Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun',
      'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'
    ];
    return `${monthNames[parseInt(month) - 1]}/${year}`;
  };

  const renderChart = () => {
    if (!data || !data.situations || data.situations.length === 0) {
      return <div className="no-data">Nenhum dado disponível</div>;
    }

    const chartWidth = 500;
    const chartHeight = 150;
    const padding = 40;
    const plotWidth = chartWidth - 2 * padding;
    const plotHeight = chartHeight - 2 * padding;

    // Get all unique profiles
    const allProfiles = Array.from(new Set(
      data.situations.flatMap(month =>
        month.datapoints.map(dp => dp.financialProfile)
      )
    ));

    // Calculate positions for each month
    const monthPositions = data.situations.map((_, index) =>
      padding + (index * plotWidth) / Math.max(data.situations.length - 1, 1)
    );

    // Find max percentage for scaling
    const maxPercentage = Math.max(
      ...data.situations.flatMap(month =>
        month.datapoints.map(dp => dp.percentage)
      )
    );

    const scaleY = (percentage: number) =>
      chartHeight - padding - (percentage / Math.max(maxPercentage, 100)) * plotHeight;

    return (
      <div className="line-chart-container">
        <svg
          width="100%"
          height="100%"
          viewBox={`0 0 ${chartWidth} ${chartHeight}`}
          preserveAspectRatio="xMidYMid meet"
        >
          {/* Grid lines */}
          {[25, 50, 75, 100].map(percentage => (
            <line
              key={percentage}
              x1={padding}
              y1={scaleY(percentage)}
              x2={chartWidth - padding}
              y2={scaleY(percentage)}
              stroke="#333344"
              strokeWidth="1"
              opacity="0.3"
            />
          ))}

          {/* Render lines for each profile */}
          {allProfiles.map(profile => {
            const profileData = data.situations.map(month => {
              const datapoint = month.datapoints.find(dp => dp.financialProfile === profile);
              return datapoint ? datapoint.percentage : 0;
            });

            const points = profileData.map((percentage, index) =>
              `${monthPositions[index]},${scaleY(percentage)}`
            ).join(' ');

            return (
              <g key={profile}>
                <polyline
                  fill="none"
                  stroke={profileColors[profile] || '#8e8e93'}
                  strokeWidth="2"
                  points={points}
                />
                {/* Data points */}
                {profileData.map((percentage, index) => {
                  const monthData = data.situations[index];
                  const datapoint = monthData.datapoints.find(dp => dp.financialProfile === profile);

                  return (
                    <circle
                      key={`${profile}-${index}`}
                      cx={monthPositions[index]}
                      cy={scaleY(percentage)}
                      r="4"
                      fill={profileColors[profile] || '#8e8e93'}
                      style={{ cursor: 'pointer' }}
                      onMouseEnter={(e) => {
                        const rect = e.currentTarget.getBoundingClientRect();
                        setTooltip({
                          x: rect.left + rect.width / 2,
                          y: rect.top - 10,
                          month: formatMonth(monthData.month),
                          profile: profileLabels[profile] || profile,
                          count: datapoint?.count || 0,
                          percentage: datapoint?.percentage || 0,
                          visible: true
                        });
                      }}
                      onMouseLeave={() => {
                        setTooltip(prev => ({ ...prev, visible: false }));
                      }}
                    />
                  );
                })}
              </g>
            );
          })}

          {/* Month labels */}
          {data.situations.map((month, index) => (
            <text
              key={month.month}
              x={monthPositions[index]}
              y={chartHeight - 10}
              textAnchor="middle"
              fontSize="10"
              fill="#8e8e93"
            >
              {formatMonth(month.month)}
            </text>
          ))}
        </svg>

        {/* Tooltip */}
        {tooltip.visible && (
          <div
            className="chart-tooltip"
            style={{
              position: 'fixed',
              left: tooltip.x,
              top: tooltip.y,
              transform: 'translateX(-50%) translateY(-100%)',
              zIndex: 1000
            }}
          >
            <div className="tooltip-content">
              <div className="tooltip-title">{tooltip.month}</div>
              <div className="tooltip-profile">{tooltip.profile}</div>
              <div className="tooltip-stats">
                <div>Quantidade: {tooltip.count}</div>
                <div>Percentual: {tooltip.percentage.toFixed(1)}%</div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <article className="card card-financial-evolution">
        <h2 className="card-title">Evolução do Perfil Financeiro</h2>
        <div className="loading-state">Carregando...</div>
      </article>
    );
  }

  if (error) {
    return (
      <article className="card card-financial-evolution">
        <h2 className="card-title">Evolução do Perfil Financeiro</h2>
        <div className="error-state">{error}</div>
      </article>
    );
  }

  return (
    <article className="card card-financial-evolution">
      <h2 className="card-title">Evolução do Perfil Financeiro</h2>
      {renderChart()}
      <div className="line-chart-legend">
        {Object.entries(profileLabels).map(([key, label]) => (
          <div key={key} className="legend-item">
            <div
              className="legend-color-box"
              style={{ backgroundColor: profileColors[key] }}
            />
            {label}
          </div>
        ))}
      </div>
    </article>
  );
};

export default FinancialEvolution;