/* FinancialEvolution Component Styles */

.line-chart-container {
  position: relative;
  width: 100%;
  height: 350px;
  overflow: visible;
  padding: 10px;
  box-sizing: border-box;
}

.line-chart-legend {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 1.5rem;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
}

.legend-color-box {
  width: 12px;
  height: 12px;
  border-radius: 3px;
}

/* Tooltip Styles */
.chart-tooltip {
  pointer-events: none;
  z-index: 1000;
}

.tooltip-content {
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.8rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
}

.tooltip-title {
  font-weight: 600;
  margin-bottom: 4px;
  color: #ffffff;
}

.tooltip-profile {
  font-weight: 500;
  margin-bottom: 4px;
  color: #e0e0e0;
}

.tooltip-stats {
  font-size: 0.75rem;
  color: #cccccc;
}

.tooltip-stats div {
  margin: 2px 0;
}

/* Loading and Error States */
.loading-state, .error-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--color-text-secondary);
  font-style: italic;
}

.error-state {
  color: #ff3b30;
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--color-text-secondary);
  font-style: italic;
}

/* --- RESPONSIVE STYLES --- */
@media (max-width: 768px) {
  .line-chart-legend {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }

  .line-chart-container {
    height: 250px;
    padding: 15px;
  }

  .tooltip-content {
    font-size: 0.75rem;
    padding: 6px 10px;
  }
}

@media (max-width: 480px) {
  .line-chart-container {
    height: 220px;
    padding: 10px;
  }

  .legend-item {
    font-size: 0.8rem;
  }
}
